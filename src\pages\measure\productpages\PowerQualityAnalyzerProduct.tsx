import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  Gauge,
  Shield,
  BarChart,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

const PowerQualityAnalyzerProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Product list for dropdown
  const productList = [
    { id: 'alm20', model: 'ALM 20', subtitle: 'Basic Power Quality Analyzer' },
    { id: 'alm31', model: 'ALM 31', subtitle: 'Standard Power Quality Analyzer' },
    { id: 'alm36', model: 'ALM 36', subtitle: 'Advanced Power Quality Analyzer' },
    { id: 'ca8345', model: 'CA 8345', subtitle: 'Premium Power Quality Analyzer' }
  ];

  // Brochure mapping for each product
  const brochureMap: Record<string, string> = {
    alm20: '/alm20p.pdf',
    alm31: '/alm31p.pdf',
    alm36: '/alm36p.pdf',
    ca8345: '/ca8345p.pdf'
  };

  // Complete product data with enhanced features and specs
  const productData = {
    alm20: {
      id: 'alm20',
      model: 'ALM 20',
      subtitle: 'Basic Power Quality Analyzer',
      image: '/alm20_1.jpg',
      images: [
        '/ALM-20-inside-01.png',
        '/ALM-20-inside-02.png' // More images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Power & Energy',
      accuracy: '±0.5%',
      //price: 'Contact for pricing',
      description: 'The ALM 20 provides essential power quality analysis in a compact, self-powered package. Perfect for long-term monitoring and basic power quality assessment with reliable performance.',
      keyFeatures: [
        'Compact self-powered design',
        'Long-term monitoring capability',
        'AC/DC voltage measurement up to 1,000V Ph-N',
        'Power measurement: kW, kVAr, kVA, PF & DPF',
        'Energy measurement: kWh, kVArh, kVAh',
        'THD & Harmonics up to 50th order',
        'User-friendly interface',
        'Portable and rugged construction',
        'Battery-powered operation',
        'Real-time data logging',
        'Automatic measurement recording',
        'Environmental protection rating',
        'Easy setup and configuration',
        'Professional measurement accuracy'
      ],
      technicalSpecs: {
        'No of Channels': '3U/3I',
        'Voltage (TRMS AC + DC)': '100V to 2000V ph-ph /50V to 1000Vph-N',
        'Voltage ratio': 'Up to 650 kV',
        'Current (TRMS AC + DC)': '5mA to 10,000 Aac / 50 mA to 5,000Adc(depending on Clamp)',
        'Current ratio': 'Up to 25 kA',
        'Frequency': '42.5-69Hz, 340 - 460Hz',
        'Power values': 'W, VA, VAr, VAD, PF, DPF, cosø, tanø',
        'Energy values': 'Wh, VAh, VArh',
        'Voltage Accuracy': '± (0.2% ±0.4V)',
        'Current Accuracy': '± 0.2% ± 0.02% Inom',
        'Active Power Accuracy (kW)': '± 0.7% ± 0.007% Pnom',
        'Harmonics, THD': 'on V, U, I & In up to 50th order',
        'Storage Capacity for a selection of parameters at 1s interval': 'Few days to few weeks',
        'Phasor Diagram': 'Yes - on PC',
        'Display': 'Back lit LCD',
        'Electrical safety': 'IEC 61010, 1000V CAT III / 600VCATIV',
        'Protection': 'IP54',
        'Communication interface': 'USB, Bluetooth, Ethernet',
        'Battery/ Battery life': '8.4V NiMh Rechargeable battery / 30minutes',
        'Dimensions / Weight': '256 x 125 x 37 mm/ 1.0kg'

      },
      applications: [
        'Basic power quality monitoring',
        'Energy consumption analysis',
        'Electrical system troubleshooting',
        'Preventive maintenance programs',
        'Small to medium facility monitoring',
        'Educational and training purposes'
      ]
    },
    alm31: {
      id: 'alm31',
      model: 'ALM 31',
      subtitle: 'Standard Power Quality Analyzer',
      image: '/alm31_10.jpg',
      images: [
        '/ALM-31-inside-01.png',
        '/ALM-31-inside-02.png'
        // Add more images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Power & Flicker',
      accuracy: '±0.5%',
      //price: 'Contact for pricing',
      description: 'The ALM 31 provides real-time visual analysis with its color display and enhanced measurement capabilities for professional power quality assessment with advanced features.',
      keyFeatures: [
        'Color display for enhanced visualization',
        'Real-time power quality analysis',
        'AC/DC voltage measurement up to 1,000V Ph-N',
        'Power measurement: kW, kVAr, kVA, PF & DPF',
        'Crest factor & K-factor measurement',
        'Short flicker measurement (Pst)',
        'Advanced data logging capabilities',
        'Professional-grade construction',
        'Enhanced user interface',
        'Comprehensive alarm system',
        'Multi-parameter display',
        'Trend analysis capability',
        'Export data functionality',
        'Real-time waveform display'
      ],
      technicalSpecs: {
        'Number of channels': 'Up to 1,000V Ph-N AC/DC',
        'Voltage (TRMS AC + DC)': 'kW, kVAr, kVA, PF & DPF',
        'Voltage ratio': ' Up to 500 kV',
        'Current (TRMS AC + DC)': '5mA to 10,000 Aac /50 mA to 5,000 Adc(depending on Clamp)',
        'Current ratio': 'Up to 60 kA',
        'Frequency': '42.5 to 69 Hz',
        'Power values / Energy values': 'W, VA, VAr, VAD, PF, DPF, cos ø, tanø / Wh, VAh, VArh, Dh',
        'Voltage Accuracy': '±(0.5% + 1V)',
        'Current Accuracy': '± 0.5% + CT Accuracy',
        'Active Power Accuracy (kW)': '±1%',
        'Harmonics, THD': 'on V, U, I, VA up to 50 order',
        'Flicker (Pst & Plt)': 'Pst',
        'Storage Capacity for a selection of parameters at 1s interval': ' 4 hours to 2 weeks',
        'Phasor Diagram': 'Yes - on instrument & on PC',
        'Display': 'Colour 1/4 VGA TFT screen, 320 x 240, diagonal 148mm',
        'Capture of screen snapshots': ' 12',
        'Electrical safety': 'IEC 61010, 1000V CAT III / 600V CAT IV',
        'Protection': 'IP53 / IK08',
        'Communication interface': 'USB',
        'Battery/ Battery life': '9.6V NiMh rechargeable battery/ Up to 13 hours',
        'Dimensions / Weight': ' 240 x 180 x 55 mm/ 1.9 kg',
        'Accessories': ' 4 - V.Probes, 4 - Cro.Clips, USB Cable, Power Adaptor, Carry Bag'
      },
      applications: [
        'Professional power quality assessment',
        'Industrial facility monitoring',
        'Power system analysis',
        'Flicker measurement studies',
        'Electrical installation verification',
        'Maintenance and troubleshooting'
      ]
    },
    alm36: {
      id: 'alm36',
      model: 'ALM 36',
      subtitle: 'Advanced Power Quality Analyzer',
      image: '/alm-36_1-1.jpg',
      images: [
        '/ALM_36_inside-01.jpg',
        '/ALM_36_inside-02.png'
        // Add more images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Transients & Harmonics',
      accuracy: '±0.2%',
      //price: 'Contact for pricing',
      description: 'The ALM 36 provides professional-grade power analysis with IEC compliance, advanced alarms, and transient capture for comprehensive quality assessment in demanding applications.',
      keyFeatures: [
        'Transient capture up to 210 counts',
        'TrueInrush function for motor analysis',
        'THD & Harmonics up to 50th order',
        'Pst & Plt flicker measurements',
        'IEC compliance for professional use',
        'Advanced alarm system',
        'Comprehensive data analysis',
        'Rugged industrial design',
        'High-resolution measurement',
        'Multi-channel analysis',
        'Automatic event detection',
        'Professional reporting tools',
        'Network connectivity options',
        'Advanced triggering system'
      ],
      technicalSpecs: {
        'Number of channels': '4U/4I',
        'Voltage (TRMS AC + DC)': '2V to 2000V ph-ph / 2V to 1000V ph-N',
        'Voltage ratio': 'Up to 500 kV',
        'Current (TRMS AC + DC)': '5 mA to 10,000 Aac/ 10 mA to 5000 Adc (depending onClamp)',
        'Current ratio': 'Up to 60 kA',
        'Frequency': '42.5 to 69 Hz',
        'Power values': 'W, VA, VAr, VAD, PF, DPF, cos ø, tan ø',
        'Energy values': 'Wh, VAh, VArh, Dh',
        'Voltage Accuracy': '± (0.5% + 1V)',
        'Current Accuracy': '± 0.5% + CT Accuracy',
        'Active Power Accuracy (kW)': '± 1%',
        'Harmonics, THD': 'on V, U, I, VA & In up to 50th order',
        'Transients': '210',
        'Flicker (Pst & Plt)': 'Pst & Plt',
        'Inrush mode': '> 10 minutes',
        'Alarms': '16,362 events',
        'Storage Capacity for a selection of parameters at 1s interval': '2 weeks to several years',
        'Phasor Diagram': 'Yes - on instrument & on PC',
        'Display': 'Colour 1/4 VGA TFT screen, 320 x 240, diagonal 148mm',
        'Capture of screen snapshots': '50',
        'Electrical safety': 'IEC 61010, 1000V CAT III / 600V CAT IV',
        'Protection': 'IP53 / IK08',
        'Communication interface': 'USB',
        'Battery/ Battery life': '9.6 V NiMh rechargeable battery / up to 13 hours',
        'Dimensions / Weight': '240 x 180 x 55 mm / 1.9 kg',
      },
      applications: [
        'Advanced power quality analysis',
        'Industrial equipment monitoring',
        'Motor and drive system analysis',
        'Power system commissioning',
        'Compliance testing and verification',
        'Critical facility monitoring'
      ]
    },
    ca8345: {
      id: 'ca8345',
      model: 'CA 8345',
      subtitle: 'Premium Power Quality Analyzer',
      image: '/ca8345_qualistar_f.jpg',
      images: [
        '/ca8345_qualistar_f.png',
        '/ca8345_qualistar_g.png' // Add more images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Class A Certified',
      accuracy: '±0.1%',
      //price: 'Contact for pricing',
      description: 'The flagship CA 8345 delivers industry-leading performance with Class A certification, advanced harmonic analysis, and superior storage capacity for the most demanding applications.',
      keyFeatures: [
        'Class A certified performance',
        'Harmonics up to 127th order',
        'Interharmonics up to 126th order',
        '2.5 μs transients capture',
        '16,362 alarms of 40 conditions',
        'Superior data storage capacity',
        'Advanced PC software included',
        'Professional certification compliance',
        'Ultra-high precision measurement',
        'Real-time spectrum analysis',
        'Advanced power quality reporting',
        'Multi-site monitoring capability',
        'Professional data export tools',
        'Industry-leading accuracy specifications'
      ],
      technicalSpecs: {
        'Inputs': 'Voltage/current, isolated',
        'Voltage': '5 V to 1,000 Vac and Vdc',
        'IEC 61000-4-30 (Ed 3) ': 'Class A (Full) ',
        'Screen': '7" colour LCD touch screen: 800 x 480 (WVGA)',
        'Clock / GPS': 'Yes, built-in',
        'Real-time mode': 'Yes',
        'Sampling rate': 'Voltage 400 kSps / Current 200 kSps /Surge 2 MSps',
        'Power mode': 'Yes',
        'Energy mode': 'Yes',
        'Unbalance mode': 'Composite ',
        'Harmonics mode': 'DC to 127rd order',
        'Interharmonics mode': '0 to 126nd order',
        'Trend recording': '> 900 parameters',
        'Alarm mode (types / number)': '52 / 20,000',
        'Carrier current detection mode': 'Yes',
        'Inrush capture (number)': '100',
        '2.5 µs transients (number) ': 'No maximum (SD card)',
        'Shockwaves': 'Up to 12 kV sampled every 500ns ',
        'EN50160 monitoring mode': 'With PAT3 software',
        'USB communication ': 'Yes',
        'SD card': 'Accessible, externa',
        'Ethernet ': 'Yes',
        'WiFi': 'Yes',
        'Web server ': 'Yes',
        'USB key port (Type A)': 'Yes',
        'Battery cartridge':' Li-ion – 5800 Ah',
        'IEC 61010 safety':' CAT IV 1000V',
        'Protection':' IP54',
        'Operating temperature': ' [+0 °C; +40 °C]',
        'Environmental compliance':' IEC 61557-12 & IEC 126586',
        'Dimensions (H x L x D)':' 200x285x55 mm / 1.9 kg '

      },
      applications: [
        'Premium power quality analysis',
        'Utility and grid monitoring',
        'Research and development',
        'High-precision measurements',
        'Certification and compliance testing',
        'Critical infrastructure monitoring'
      ]
    },
    // alm36: {
    //   id: 'alm36',
    //   model: 'ALM 36',
    //   subtitle: 'Advanced Power Quality Analyzer',
    //   image: '/alm-36_1-1.jpg',
    //   images: [
    //     '/alm-36_1-1.jpg',
    //     '/alm-36_1-2.jpg'
    //     // Add more images when available
    //   ],
    //   voltage: '1,000V Ph-N',
    //   measurement: 'Transients & Harmonics',
    //   accuracy: '±0.2%',
    //   //price: 'Contact for pricing',
    //   description: 'The ALM 36 offers advanced transient capture, true inrush analysis, and comprehensive harmonic analysis for professional power quality assessment in demanding applications.',}

  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/power-quality-analyzers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Power Quality Analyzer`;
    }
  }, [product, navigate]);

  // Handle clicking outside dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (dropdownOpen && !target.closest('.dropdown-container')) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Handle image loading errors
  const handleImageError = () => {
    setImageError(true);
  };

  // Get fallback image
  const getFallbackImage = () => {
    return 'https://via.placeholder.com/300x200/FFD700/000000?text=No+Image';
  };

  // Feature icon logic similar to OscilloscopeProduct
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('color') || feature.toLowerCase().includes('touchscreen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('logging') || feature.toLowerCase().includes('data')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('communication') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('ethernet') || feature.toLowerCase().includes('wifi') || feature.toLowerCase().includes('connectivity')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('power') || feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('self-powered')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('environmental')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('transient') || feature.toLowerCase().includes('alarm')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('measurement') || feature.toLowerCase().includes('accuracy') || feature.toLowerCase().includes('harmonics') || feature.toLowerCase().includes('analysis')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('safety') || feature.toLowerCase().includes('compliance') || feature.toLowerCase().includes('iec') || feature.toLowerCase().includes('certified')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('flicker') || feature.toLowerCase().includes('trend') || feature.toLowerCase().includes('spectrum') || feature.toLowerCase().includes('waveform')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  // Get the brochure URL for the current product, fallback to undefined if not found
  const brochureUrl = brochureMap[product.id];

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Power Quality Analyzers
              </h1>
              <p className="typography-h4 text-black">
                Professional Power Analysis Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block dropdown-container">
                <div className="relative w-full md:w-auto group">
                  <button
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => {
                            setDropdownOpen(false);
                            navigate(`/measure/power-quality-analyzers/product/${prod.id}`);
                          }}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/power-quality-analyzers')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Voltage Range</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Accuracy</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    {/* <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div> */}
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    {brochureUrl && (
                      <button
                        onClick={() => window.open(brochureUrl, '_blank')}
                        className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90"
                        style={{ backgroundColor: '#F5C842' }}
                      >
                        <Download className="h-5 w-5" />
                        <span>View Brochure</span>
                      </button>
                    )}
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div
                  className={`w-full ${product.id === 'alm20' ? '' : 'max-w-xs'}`}
                  style={
                    product.id === 'alm20'
                      ? { maxWidth: '350px' } // Increased from 220px to 350px
                      : {}
                  }
                >
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={imageError ? getFallbackImage() : product.image}
                      alt={product.model}
                      className="object-contain"
                      style={
                        product.id === 'alm20'
                          ? {
                              width: '350px', // Increased from 800px to 350px for a more reasonable large size
                              height: '350px',
                              background: 'transparent',
                              mixBlendMode: imageError ? 'normal' : 'multiply',
                              filter: imageError ? 'none' : 'brightness(1.1) contrast(1.1)',
                              opacity: '0.95',
                            }
                          : {
                              maxHeight: '800px',
                              maxWidth: '800px',
                              background: 'transparent',
                              mixBlendMode: imageError ? 'normal' : 'multiply',
                              filter: imageError ? 'none' : 'brightness(1.1) contrast(1.1)',
                              opacity: '0.95',
                            }
                      }
                      onError={handleImageError}
                      onLoad={() => setImageError(false)}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Applications Section - Side by Side */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Key Features Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                </div>
                
                {/* Content Area */}
                <div className="flex-1 flex flex-col">
                  <div className="px-6 pb-6 space-y-4 flex-1">
                    {product.keyFeatures.slice(0, 6).map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.05 }}
                        className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                          <FeatureIcon feature={feature} />
                        </div>
                        <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                  
                  {/* Expandable Features */}
                  {product.keyFeatures.length > 6 && (
                    <AnimatePresence>
                      {featuresExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                            {product.keyFeatures.slice(6).map((feature, index) => (
                              <motion.div
                                key={index + 6}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.4, delay: index * 0.05 }}
                                className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                              >
                                <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                  <FeatureIcon feature={feature} />
                                </div>
                                <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}

                  {/* Show More/Less Button */}
                  {product.keyFeatures.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                        style={{ backgroundColor: '#F5C842' }}
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.keyFeatures.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Applications Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Applications</h2>
                </div>
                <div className="px-6 pb-6 flex-1">
                  <div className="space-y-4">
                    {product.applications.map((application, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                      >
                        <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                        <span className="text-gray-700 font-medium">{application}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Technical Specifications Section - Centered Below */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-2xl shadow-lg overflow-hidden"
            >
              {/* Header */}
              <div className="p-6 text-center">
                <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
              </div>
              
              {/* Content */}
              <div className="px-6 pb-6">
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <tbody>
                      {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                        <motion.tr
                          key={key}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="border-b border-gray-200 hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <td className="py-3 px-4 font-semibold text-gray-900 border-r border-gray-200 w-1/2">
                            {key}
                          </td>
                          <td className="py-3 px-4 text-gray-700">
                            {value}
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Expandable Specifications */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <AnimatePresence>
                    {specsExpanded && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse">
                            <tbody>
                              {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                <motion.tr
                                  key={key}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="border-b border-gray-200 hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <td className="py-3 px-4 font-semibold text-gray-900 border-r border-gray-200 w-1/2">
                                    {key}
                                  </td>
                                  <td className="py-3 px-4 text-gray-700">
                                    {value}
                                  </td>
                                </motion.tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                )}

                {/* Show More/Less Button */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="mt-6 border-t border-gray-100 pt-4">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg border border-yellow-200 hover:border-yellow-300"
                      style={{ backgroundColor: '#F5C842' }}
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on power quality analyzer solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Phone className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default PowerQualityAnalyzerProduct;